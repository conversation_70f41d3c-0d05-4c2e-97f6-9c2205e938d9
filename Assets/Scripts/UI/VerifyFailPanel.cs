using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace QFramework.ZSS
{
	public class VerifyFailPanelData : UIPanelData
	{
	}
	public partial class VerifyFailPanel : UIPanel, IController
	{
		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as VerifyFailPanelData ?? new VerifyFailPanelData();
			// please add init code here
			Btn_Close.onClick.AddListener(() =>
			{
				SyncUIKit.ClosePanel<VerifyFailPanel>();
			});
			Btn_Ok.onClick.AddListener(() =>
			{
				SyncUIKit.ClosePanel<VerifyFailPanel>();

			});
		}
		
		protected override void OnOpen(IUIData uiData = null)
	{
		// 打开预约失败面板时禁用世界点击事件
		GetArchitecture().SendEvent(new ClickEnableEvent(false));
	}
		
		protected override void OnShow()
		{
		}
		
		protected override void OnHide()
	{
		// 隐藏面板时重新启用世界点击事件
		GetArchitecture().SendEvent(new ClickEnableEvent(true));
	}
		
		protected override void OnClose()
	{
		// 关闭面板时重新启用世界点击事件
		GetArchitecture().SendEvent(new ClickEnableEvent(true));
	}

	public IArchitecture GetArchitecture()
	{
		return LibraryApp.Interface;
	}
	}
}
