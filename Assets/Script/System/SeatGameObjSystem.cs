
using MoonSharp.Interpreter;
using System;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;
namespace QFramework.ZSS
{
    /// <summary>
    /// 座位游戏对象系统
    /// </summary>
    public class SeatGameObjSystem : AbstractSystem
    {

        /// <summary>
        ///   座位点对象
        /// </summary>
        public Dictionary<SceneType, Dictionary<string ,GameObject>> SeatGameObjDic =  new Dictionary<SceneType, Dictionary<string, GameObject>>();

        /// <summary>
        /// 场景座位类别对象
        /// </summary>
        public Dictionary<SceneType, Dictionary<SeatType, List<SeatController>>> SeatGameobjTypeDic = new Dictionary<SceneType, Dictionary<SeatType, List<SeatController>>>();
       
        
        /// <summary>
        /// 角色对象
        /// </summary>
        public Dictionary<string, GameObject> PlayerDic = new Dictionary<string, GameObject>();


        /// <summary>
        /// 各种座位样式
        /// </summary>
        public Dictionary<string, GameObject> SeatDic = new Dictionary<string, GameObject>();

        /// <summary>
        /// 各种道具
        /// </summary>
        public Dictionary<AnimationTypeState, GameObject> PropertyDic = new Dictionary<AnimationTypeState, GameObject>();


        /// <summary>
        /// 个人界面角色对象
        /// </summary>
        public Dictionary<string, GameObject>OnselfPlayerDic = new Dictionary<string, GameObject>();



        /// <summary>
        /// 个人界面各种座位样式
        /// </summary>
        public Dictionary<string, GameObject> OnselfSeatDic = new Dictionary<string, GameObject>();

        protected override void OnInit()
        {
         
        }




        // 初始化字典
        public void InitializeSeatMap()
        {
            SeatController[] seats = Resources.FindObjectsOfTypeAll<SeatController>();
            InjectSeatsIntoDictionary(seats);
        }

        // 将 SeatController 对象注入到字典中
        private void InjectSeatsIntoDictionary(SeatController[] seats)
        {
            SeatGameobjTypeDic.Clear();
            foreach (SeatController seat in seats)
            {
                // 如果场景类型不存在，则初始化
                if (!SeatGameobjTypeDic.ContainsKey(seat.sceneType))
                {
                    SeatGameobjTypeDic[seat.sceneType] = new Dictionary<SeatType, List<SeatController>>();
                }

                // 如果座椅类型不存在，则初始化
                if (!SeatGameobjTypeDic[seat.sceneType].ContainsKey(seat.seatType))
                {
                    SeatGameobjTypeDic[seat.sceneType][seat.seatType] = new List<SeatController>();
                }

                // 将 SeatController 添加到对应的列表中
                SeatGameobjTypeDic[seat.sceneType][seat.seatType].Add(seat);
            }
          
        }




        // 根据场景、座椅类型和ID查找座椅对象
        public GameObject FindSeat(SceneType scene, SeatType seatType, string seatID)
        {
            if (SeatGameobjTypeDic.ContainsKey(scene) && SeatGameobjTypeDic[scene].ContainsKey(seatType))
            {
                foreach (var seatInfo in SeatGameobjTypeDic[scene][seatType])
                {
                    if (seatInfo.SeatID == seatID)
                    {
                        return seatInfo.gameObject;
                    }
                }
            }
            Debug.LogWarning($"Seat not found: Scene={scene}, SeatType={seatType}, SeatID={seatID}");
            return null;
        }

        
        /// <summary>
        /// 根据场景类型和座椅类型获取座椅控制器列表
        /// </summary>
        /// <param name="sceneType"></param>
        /// <param name="seatType"></param>
        /// <returns></returns>
        public List<SeatController> GetSeatControllers(SceneType sceneType, SeatType seatType)
        {
            if (SeatGameobjTypeDic.TryGetValue(sceneType, out var seatTypeDic))
            {
                if (seatTypeDic.TryGetValue(seatType, out var seatControllers))
                {
                    return seatControllers;
                }
            }

            // 如果没有找到对应的列表，可以返回一个空列表或者抛出异常
            return new List<SeatController>();
        }


        // 取出座位对象的方法
        public GameObject GetSeatGameObject(SceneType sceneType, string seatName)
        {
            // 检查外层字典是否包含指定的 SceneType
            if (SeatGameObjDic.ContainsKey(sceneType))
            {
                // 获取内层字典
                Dictionary<string, GameObject> seatDic = SeatGameObjDic[sceneType];

                // 检查内层字典是否包含指定的座位名称
                if (seatDic.ContainsKey(seatName))
                {
                    // 返回对应的 GameObject
                    return seatDic[seatName];
                }
                else
                {
                    Debug.LogWarning($"场景 {sceneType} 中未找到座位 {seatName}！");
                    return null;
                }
            }
            else
            {
                Debug.LogWarning($"未找到场景 {sceneType}！");
                return null;
            }
        }


        /// <summary>
        /// 根据玩家ID获取角色对象
        /// </summary>
        /// <param name="playerID">玩家ID</param>
        /// <returns>对应的角色对象，如果未找到则返回null</returns>
        public GameObject GetPlayerByID(string playerID)
        {
            if (PlayerDic.ContainsKey(playerID))
            {
                return PlayerDic[playerID];
            }
            Debug.LogWarning($"Player not found: PlayerID={playerID}");
            return null;
        }

        public GameObject GetPropertyByAnimationTypeState(AnimationTypeState state)
        {
            if (PropertyDic.ContainsKey(state))
            {
                return PropertyDic[state];
            }
            Debug.LogWarning($"Property not found: AnimationTypeState={state}");
            return null;
        }

        /// <summary>
        /// 实例化座位游戏对象
        /// </summary>
        /// <param name="seatEs"></param>
        public GameObject CreateGameObj(GameObject gameObject ,Transform parent) 
        {
            var go = GameObject.Instantiate(gameObject);

            go.transform.SetParent(parent);
            // 将局部坐标归零
            go.transform.localPosition = Vector3.zero;

            // 如果需要，也可以将旋转和缩放归零
            go.transform.localRotation = Quaternion.identity;
            go.transform.localScale = Vector3.one;
            go.SetActive(true);
            return go;
        }


 
        //TODO:
        /// <summary>
        /// 播放女孩坐下动画
        /// </summary>
        /// <param name="SetDataID">座位编号</param>
        public void PlayAnimationDown(SeatType seatType, string SetDataID, string PlayerID, AnimationTypeState State, UserImageVO userdata)
        {
            var Go = GetSeatGameObject(this.GetSystem<ScenceSystem>().CurrentScene, SetDataID);
            //当前座位下没有子对象
            if (Go.transform.childCount == 0)
            {
                //创建对象
                //获取角色 并实例化
                var play = GetPlayerByID(PlayerID);
                if (play != null)
                {
                    //创建成功，播放动画
                   var CreatPlayer= CreateGameObj(play, Go.transform);
                    CreatPlayer.GetComponent<PlayController>().PlayDown(Go.GetComponent<SetData>().Type,State);
                    //添加文字和头像
                    NamePlateManager.Instance.AddNameplate(CreatPlayer.transform, userdata.nickName, userdata.avatarInfo);
                }
                //锁定当前座位
                FindSeat(this.GetSystem<ScenceSystem>().CurrentScene, seatType, SetDataID).GetComponent<SeatController>().UpdataSeatData(0);


            }
        }

        /// <summary>
        /// 播放女孩起身动画
        /// </summary>
        /// <param name="SetDataID"></param>
        public void PlaylAnimationUp(SeatType seatType, string SetDataID)
        {
            var Go= GetSeatGameObject(this.GetSystem<ScenceSystem>().CurrentScene, SetDataID);
            //当前座位下没有子对象
            if (Go.transform.childCount == 0)
            {
                ////创建对象
                return;
            }
            else
            {
              
                Go.transform.GetChild(0).GetComponent<PlayController>().PlayUp(Go.GetComponent<SetData>().Type);
                //解锁当前座位
                FindSeat(this.GetSystem<ScenceSystem>().CurrentScene, seatType, SetDataID).GetComponent<SeatController>().UpdataSeatData(0);

            }

        }

        /// <summary>
        /// 播放Stand动画
        /// </summary>
        /// <param name="SceneType"></param>
        /// <param name="SetDataID"></param>
        /// <param name="State"></param>
        public void PlayAnimationStand(SceneType SceneType,string SetDataID, string PlayerID , AnimationTypeState State, UserImageVO userdata)
        {
            var Go = GetSeatGameObject(SceneType, SetDataID);
            //当前座位下没有子对象
            if (Go.transform.childCount == 0)
            {
                //创建对象
                //获取角色 并实例化
                var play=  GetPlayerByID(PlayerID);
                if (play != null)
                {
                    var CreatPlayer = CreateGameObj(play, Go.transform);
                    //创建成功，播放动画
                    CreatPlayer.GetComponent<PlayController>().PlayStand(State);
                    //添加文字和头像
                    NamePlateManager.Instance.AddNameplate(CreatPlayer.transform, userdata.nickName, userdata.avatarInfo);
                }
            }
      
        }

        /// <summary>
        /// 座位标签转String
        /// </summary>
        /// <param name="SeatType"></param>
        /// <returns></returns>
        public string SeatTypeToDeskID( SeatType SeatType)
        {
            switch (SeatType)
            {
                case SeatType.A:
                    return "3001";
                case SeatType.B:
                    return "3002";
                case SeatType.C:
                    return "3003";
                default:
                    return "3001";
            }
        }

        public SeatType DeskIDToSeatType(string DeskID)
        {
            switch (DeskID)
            {
                case "3001":
                    return SeatType.A;
                case "3002":
                    return SeatType.B;
                 case "3003":
                    return SeatType.C;
                default:
                    return SeatType.A;
            }
        }

        /// <summary>
        /// 跟新座位和人物数据
        /// </summary>
        public void UpdataPlayerAndSeatData(Dictionary<string, SeatData>  data)
        { 
            //生成玩家形象对象
            foreach (var item in data)
            {
                //修改座位样式
                ChangeSeatType(item.Value.id, DeskIDToSeatType(item.Value.userImageVO.deskAssetId));
                //在座位上生成对应的对象
                ////且在座位上生成有人的对象 ,证明有人
                if (item.Value.status != 0)
                {
                    int randomInt = UnityEngine.Random.Range(0, 3);
                    ////Debug.Log(randomInt);
                    // this.GetSystem<SeatGameObjSystem>().PlayAnimationStand(this.GetSystem<ScenceSystem>().CurrentScene, item.Value.id, item.Value.userImageVO.clothesAssetId, (AnimationTypeState)randomInt);
                    //锁定当前座位
                     this.GetSystem<SeatGameObjSystem>().PlayAnimationDown(DeskIDToSeatType(item.Value.userImageVO.deskAssetId), item.Value.id, item.Value.userImageVO.clothesAssetId, (AnimationTypeState)randomInt, item.Value.userImageVO);

                }
                FindSeat(this.GetSystem<ScenceSystem>().CurrentScene, DeskIDToSeatType(item.Value.userImageVO.deskAssetId), item.Value.id).GetComponent<SeatController>().UpdataSeatData(item.Value.status);

            }
        }

        /// <summary>
        /// 修改指定ID的座位的样式
        /// </summary>
        /// <param name="SeatID"></param>
        /// <param name="seatType"></param>
        public void ChangeSeatType(string SeatID, SeatType seatType )
        {  
            //当前选择的样式激活
            var Find = FindSeat(this.GetSystem<ScenceSystem>().CurrentScene, seatType, SeatID);
            Find.gameObject.SetActive(true);
            //其他样式的座椅隐藏
            // 遍历枚举
            foreach (SeatType type in Enum.GetValues(typeof(SeatType)))
            {
                if (type != seatType)
                {
                    var go = FindSeat(this.GetSystem<ScenceSystem>().CurrentScene, type, SeatID);
                    go.gameObject.SetActive(false);
                }
            }
        }

       

        //------------------------------------个人界面显示 的书桌和人物对象-----------------------------------



       
    }
}

