using UnityEngine;
using QFramework;

namespace QFramework.ZSS
{
    /// <summary>
    /// SyncUIManager 测试脚本
    /// 用于验证同步UI管理器的功能
    /// </summary>
    public class SyncUIManager_Test : MonoBehaviour
    {
        [Header("测试配置")]
        public bool enableTestOnStart = false;
        public KeyCode testKey = KeyCode.T;
        
        void Start()
        {
            if (enableTestOnStart)
            {
                TestSyncUIManager();
            }
        }
        
        void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                TestSyncUIManager();
            }
        }
        
        /// <summary>
        /// 测试同步UI管理器的各项功能
        /// </summary>
        public void TestSyncUIManager()
        {
            Debug.Log("=== 开始测试 SyncUIManager ===");
            
            // 测试1: 预加载面板
            Debug.Log("测试1: 预加载面板");
            SyncUIManager.Instance.PreloadPanel<SignPanel>();
            SyncUIManager.Instance.PreloadPanel<MakePanel>();
            
            // 测试2: 同步打开面板
            Debug.Log("测试2: 同步打开面板");
            var signPanel = SyncUIKit.OpenPanel<SignPanel>();
            if (signPanel != null)
            {
                Debug.Log("SignPanel 打开成功");
            }
            else
            {
                Debug.LogError("SignPanel 打开失败");
            }
            
            // 测试3: 检查面板是否打开
            Debug.Log("测试3: 检查面板状态");
            bool isSignPanelOpen = SyncUIKit.IsPanelOpen<SignPanel>();
            Debug.Log($"SignPanel 是否打开: {isSignPanelOpen}");
            
            // 测试4: 获取面板
            Debug.Log("测试4: 获取面板");
            var retrievedPanel = SyncUIKit.GetPanel<SignPanel>();
            if (retrievedPanel != null)
            {
                Debug.Log("成功获取到 SignPanel 实例");
            }
            
            // 测试5: 隐藏面板
            Debug.Log("测试5: 隐藏面板");
            SyncUIKit.HidePanel<SignPanel>();
            
            // 测试6: 显示面板
            Debug.Log("测试6: 显示面板");
            SyncUIKit.ShowPanel<SignPanel>();
            
            // 测试7: 关闭面板
            Debug.Log("测试7: 关闭面板");
            SyncUIKit.ClosePanel<SignPanel>();
            
            // 验证面板是否已关闭
            bool isSignPanelOpenAfterClose = SyncUIKit.IsPanelOpen<SignPanel>();
            Debug.Log($"关闭后 SignPanel 是否还打开: {isSignPanelOpenAfterClose}");
            
            Debug.Log("=== SyncUIManager 测试完成 ===");
        }
        
        /// <summary>
        /// 测试带参数的面板打开
        /// </summary>
        public void TestPanelWithData()
        {
            Debug.Log("=== 测试带参数的面板打开 ===");
            
            // 测试带数据的面板打开
            var venueSelectPanelData = new VenueSelectPanelData { DataList = null };
            var venuePanel = SyncUIKit.OpenPanel<VenueSelectPanel>(UILevel.Common, venueSelectPanelData);
            
            if (venuePanel != null)
            {
                Debug.Log("VenueSelectPanel 带参数打开成功");
            }
            else
            {
                Debug.LogError("VenueSelectPanel 带参数打开失败");
            }
        }
        
        /// <summary>
        /// 测试多个面板的管理
        /// </summary>
        public void TestMultiplePanels()
        {
            Debug.Log("=== 测试多个面板管理 ===");
            
            // 打开多个面板
            SyncUIKit.OpenPanel<SignPanel>();
            SyncUIKit.OpenPanel<MakePanel>();
            
            // 隐藏所有面板
            SyncUIManager.Instance.HideAllPanels();
            Debug.Log("所有面板已隐藏");
            
            // 关闭所有面板
            SyncUIManager.Instance.CloseAllPanels();
            Debug.Log("所有面板已关闭");
        }
        
        /// <summary>
        /// 性能测试
        /// </summary>
        public void PerformanceTest()
        {
            Debug.Log("=== 性能测试 ===");
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 测试同步打开面板的性能
            for (int i = 0; i < 10; i++)
            {
                var panel = SyncUIKit.OpenPanel<SignPanel>();
                SyncUIKit.ClosePanel<SignPanel>();
            }
            
            stopwatch.Stop();
            Debug.Log($"同步打开/关闭面板10次耗时: {stopwatch.ElapsedMilliseconds}ms");
        }
    }
}