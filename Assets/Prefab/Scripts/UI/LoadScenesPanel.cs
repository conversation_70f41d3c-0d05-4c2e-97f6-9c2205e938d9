using UnityEngine;
using UnityEngine.UI;
using QFramework;
using System;
using System.Collections;

namespace QFramework.ZSS
{
	public class LoadScenesPanelData : UIPanelData
	{
        public AsyncOperation asyncOperation;
        public Action action;
        public Action OpenAction;
    }
	public partial class LoadScenesPanel : UIPanel,IController
	{
        private LoadScenesPanelData m_Data;
        private float currentProgress = 0f;
        private float MainProgress = 0f;
        
        // 新增：阶段定义
        private enum LoadingStage
        {
            SceneLoading,    // 场景加载
            UILoading,       // UI加载
            DataLoading,     // 数据加载
            Preparing        // 准备进入
        }
        
        private LoadingStage currentStage = LoadingStage.SceneLoading;
        
		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as LoadScenesPanelData ?? new LoadScenesPanelData();
			// please add init code here
		}
		
		protected override void OnOpen(IUIData uiData = null)
		{
            m_Data = uiData as LoadScenesPanelData;
            if (m_Data != null)
            {
                m_Data.OpenAction?.Invoke();
                MainProgress = 0f;
            }
        }
		
		protected override void OnShow()
		{
            if (m_Data != null)
            {
                currentProgress = 0f;
                currentStage = LoadingStage.SceneLoading;
                m_Data.asyncOperation.allowSceneActivation = false;
                StartCoroutine(LoadSceneAsync());
            }
        }
		
		protected override void OnHide()
		{
		}
		
		protected override void OnClose()
		{
            // 关闭加载面板后不再自动重开，避免页面闪回
        }
        
        // 优化后的加载协程 - 基于真实进度和阶段
        IEnumerator LoadSceneAsync()
        {
            // 阶段1：场景加载 (0-60%)
            currentStage = LoadingStage.SceneLoading;
            while (m_Data.asyncOperation.progress < 0.9f)
            {
                // 直接使用真实进度，不用Lerp平滑
                float realProgress = m_Data.asyncOperation.progress * 0.6f; // 场景加载占60%
                Fill.fillAmount = realProgress;
                Txt_Loding.text = "场景加载..." + (realProgress * 100).ToString("f0") + "%";
                yield return null;
            }
            
            // 允许场景激活
            m_Data.asyncOperation.allowSceneActivation = true;
            
            // 等待场景完全加载
            while (!m_Data.asyncOperation.isDone)
            {
                float realProgress = 0.6f; // 保持在60%
                Fill.fillAmount = realProgress;
                Txt_Loding.text = "场景加载..." + (realProgress * 100).ToString("f0") + "%";
                yield return null;
            }
            
            // 阶段2：UI加载 (60-80%)
            currentStage = LoadingStage.UILoading;
            yield return StartCoroutine(SimulateUILoading());
            
            // 阶段3：数据加载 (80-95%)
            currentStage = LoadingStage.DataLoading;
            yield return StartCoroutine(SimulateDataLoading());
            
            // 阶段4：准备进入 (95-100%)
            currentStage = LoadingStage.Preparing;
            yield return StartCoroutine(SimulatePreparing());
            
            // 加载完成关闭
            SyncUIKit.ClosePanel<LoadScenesPanel>();
            m_Data.action?.Invoke();
        }
        
        // 模拟UI加载阶段
        IEnumerator SimulateUILoading()
        {
            float startProgress = 0.6f;
            float endProgress = 0.8f;
            float duration = 0.5f; // 0.5秒完成UI加载
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = Mathf.Lerp(startProgress, endProgress, elapsed / duration);
                Fill.fillAmount = progress;
                Txt_Loding.text = "UI加载..." + (progress * 100).ToString("f0") + "%";
                yield return null;
            }
        }
        
        // 模拟数据加载阶段
        IEnumerator SimulateDataLoading()
        {
            float startProgress = 0.8f;
            float endProgress = 0.95f;
            float duration = 0.3f; // 0.3秒完成数据加载
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = Mathf.Lerp(startProgress, endProgress, elapsed / duration);
                Fill.fillAmount = progress;
                Txt_Loding.text = "数据加载..." + (progress * 100).ToString("f0") + "%";
                yield return null;
            }
        }
        
        // 模拟准备进入阶段
        IEnumerator SimulatePreparing()
        {
            float startProgress = 0.95f;
            float endProgress = 1.0f;
            float duration = 0.2f; // 0.2秒完成准备
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = Mathf.Lerp(startProgress, endProgress, elapsed / duration);
                Fill.fillAmount = progress;
                Txt_Loding.text = "准备进入..." + (progress * 100).ToString("f0") + "%";
                yield return null;
            }
        }

        public IArchitecture GetArchitecture()
        {
            return LibraryApp.Interface;
        }
    }
}
