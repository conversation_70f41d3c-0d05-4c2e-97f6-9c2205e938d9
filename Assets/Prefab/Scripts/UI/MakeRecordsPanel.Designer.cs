using System;
using UnityEngine;
using UnityEngine.UI;
using Q<PERSON>ramework;

namespace QFramework.ZSS
{
	// Generate Id:ebb0b9da-f1dd-4efa-9e52-e0e54c42a555
	public partial class MakeRecordsPanel
	{
		public const string Name = "MakeRecordsPanel";
		
		[SerializeField]
		public UnityEngine.UI.Toggle Toggle_All;
		[SerializeField]
		public UnityEngine.UI.Image All_OFF;
		[SerializeField]
		public UnityEngine.UI.Image All_ON;
		[SerializeField]
		public UnityEngine.UI.Toggle Toggle_Stay;
		[SerializeField]
		public UnityEngine.UI.Image Stay_ON;
		[SerializeField]
		public UnityEngine.UI.Image Stay_OFF;
		[SerializeField]
		public UnityEngine.UI.Toggle Toggle_Complete;
		[SerializeField]
		public UnityEngine.UI.Image Complete_OFF;
		[SerializeField]
		public UnityEngine.UI.Image Complete_ON;
		[SerializeField]
		public UnityEngine.UI.ScrollRect ScrollParent;
		[SerializeField]
		public RectTransform Content;
		[SerializeField]
		public UnityEngine.UI.Image Img_Min;
		[SerializeField]
		public UnityEngine.UI.Image Img_Big;
		[SerializeField]
		public UnityEngine.UI.Button Btn_Cancel;
		[SerializeField]
		public UnityEngine.UI.Image Stay;
		[SerializeField]
		public UnityEngine.UI.Image Cancel;
		[SerializeField]
		public UnityEngine.UI.Image Succes;
		[SerializeField]
		public UnityEngine.UI.Image Overdue;
		[SerializeField]
		public UnityEngine.UI.Image Party_Avatar;
		[SerializeField]
		public UnityEngine.UI.Image Round_Avatar;
		[SerializeField]
		public UnityEngine.UI.Text Txt_Name;
		[SerializeField]
		public UnityEngine.UI.Text Txt_Data;
		[SerializeField]
		public UnityEngine.UI.Text Txt_Time;
		[SerializeField]
		public UnityEngine.UI.Text Txt_SeatName;
		[SerializeField]
		public UnityEngine.UI.Image None_Data;
		[SerializeField]
		public UnityEngine.UI.Button Btn_Back;
		
		private MakeRecordsPanelData mPrivateData = null;
		
		protected override void ClearUIComponents()
		{
			Toggle_All = null;
			All_OFF = null;
			All_ON = null;
			Toggle_Stay = null;
			Stay_ON = null;
			Stay_OFF = null;
			Toggle_Complete = null;
			Complete_OFF = null;
			Complete_ON = null;
			ScrollParent = null;
			Content = null;
			Img_Min = null;
			Img_Big = null;
			Btn_Cancel = null;
			Stay = null;
			Cancel = null;
			Succes = null;
			Overdue = null;
			Party_Avatar = null;
			Round_Avatar = null;
			Txt_Name = null;
			Txt_Data = null;
			Txt_Time = null;
			Txt_SeatName = null;
			None_Data = null;
			Btn_Back = null;
			
			mData = null;
		}
		
		public MakeRecordsPanelData Data
		{
			get
			{
				return mData;
			}
		}
		
		MakeRecordsPanelData mData
		{
			get
			{
				return mPrivateData ?? (mPrivateData = new MakeRecordsPanelData());
			}
			set
			{
				mUIData = value;
				mPrivateData = value;
			}
		}
	}
}
